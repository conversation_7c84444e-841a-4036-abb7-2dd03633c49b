name: PaddleOCR PR Tests

on:
  push:
  pull_request:
    branches: ["main", "release/*"]

permissions:
  contents: read

jobs:
  test-pr:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        pip install "paddlepaddle==2.5" requests
        pip install -e .
    - name: Test with pytest
      run: |
        pytest tests/
