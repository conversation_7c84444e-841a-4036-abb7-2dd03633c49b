import warnings
import os
import torch
import gc
warnings.filterwarnings("ignore")
from ultralytics import YOLO

# 设置环境变量来解决内存碎片化问题
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

def clear_memory():
    """全面清理内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    gc.collect()

def get_optimal_batch_size():
    """根据GPU内存自动计算最优批次大小"""
    if not torch.cuda.is_available():
        return 1
    
    gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    if gpu_memory_gb >= 24:
        return 16
    elif gpu_memory_gb >= 16:
        return 12
    elif gpu_memory_gb >= 12:
        return 8
    elif gpu_memory_gb >= 8:
        return 4
    else:
        return 2

def get_optimal_image_size():
    """根据GPU内存自动计算最优图像尺寸"""
    if not torch.cuda.is_available():
        return 320
    
    gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    if gpu_memory_gb >= 16:
        return 640
    elif gpu_memory_gb >= 12:
        return 512
    elif gpu_memory_gb >= 8:
        return 416
    else:
        return 320

def print_gpu_info():
    """打印GPU信息"""
    if torch.cuda.is_available():
        device = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(device)
        gpu_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
        print(f"GPU: {gpu_name}")
        print(f"GPU内存: {gpu_memory:.1f} GB")
    else:
        print("CUDA不可用")

if __name__ == "__main__":
    print("=== GPU信息 ===")
    print_gpu_info()
    
    # 清理内存
    clear_memory()
    
    # 自动计算最优参数
    optimal_batch = get_optimal_batch_size()
    optimal_imgsz = get_optimal_image_size()
    
    print(f"\n=== 优化参数 ===")
    print(f"推荐批次大小: {optimal_batch}")
    print(f"推荐图像尺寸: {optimal_imgsz}")
    
    try:
        model = YOLO(model=r'F:\qyp\ultralytics\ultralytics\cfg\models\11\try1.yaml')
        
        print(f"\n=== 开始训练 ===")
        model.train(
            data=r"F:\qyp\ultralytics\ultralytics\cfg\datasets\ronghe.yaml", 
            imgsz=optimal_imgsz,
            epochs=10,
            batch=optimal_batch,
            workers=2,  # 减少worker数量
            device=0,
            amp=True,   # 混合精度训练
            cache=False,  # 不缓存数据集到内存
            save_period=5,  # 每5个epoch保存一次
        )
        
        # 训练完成后清理内存
        clear_memory()
        
        print(f"\n=== 开始验证 ===")
        model.val(
            data=r"F:\qyp\ultralytics\ultralytics\cfg\datasets\ronghe.yaml",
            imgsz=optimal_imgsz,
            batch=max(1, optimal_batch // 2),  # 验证时使用更小的批次
            device=0
        )
        
    except torch.cuda.OutOfMemoryError as e:
        print(f"\n=== 内存不足错误 ===")
        print(f"错误信息: {e}")
        print("\n建议解决方案:")
        print("1. 进一步减少批次大小")
        print("2. 减少图像尺寸")
        print("3. 关闭其他占用GPU内存的程序")
        print("4. 重启Python环境")
        
    except Exception as e:
        print(f"\n=== 其他错误 ===")
        print(f"错误信息: {e}")
    
    finally:
        # 最终清理
        clear_memory()
        print("\n=== 训练完成，内存已清理 ===")
