Global:
  use_gpu: True
  epoch_num: 6
  log_smooth_window: 20
  print_batch_step: 50
  save_model_dir: ./output/rec/rec_resnet_rfl_att/
  save_epoch_step: 1
  # evaluation is run every 5000 iterations after the 4000th iteration
  eval_batch_step: [0, 5000]
  cal_metric_during_train: True
  pretrained_model: ./pretrain_models/rec_resnet_rfl_visual/best_accuracy.pdparams 
  checkpoints:
  save_inference_dir:
  use_visualdl: False
  infer_img: doc/imgs_words_en/word_10.png
  # for data or label process
  character_dict_path:
  max_text_length: 25
  infer_mode: False
  use_space_char: False
  save_res_path: ./output/rec/rec_resnet_rfl.txt


Optimizer:
  name: AdamW
  beta1: 0.9
  beta2: 0.999
  weight_decay: 0.0
  clip_norm_global: 5.0
  lr:
    name: Piecewise
    decay_epochs : [3, 4, 5]
    values : [0.001, 0.0003, 0.00009, 0.000027]

Architecture:
  model_type: rec
  algorithm: RFL
  in_channels: 1
  Transform:
    name: TPS
    num_fiducial: 20
    loc_lr: 1.0
    model_name: large
  Backbone:
    name: ResNetRFL
    use_cnt: True
    use_seq: True
  Neck:
    name: RFAdaptor
    use_v2s: True
    use_s2v: True
  Head:
    name: RFLHead  
    in_channels: 512
    hidden_size: 256
    batch_max_legnth: 25
    out_channels: 38
    use_cnt: True
    use_seq: True

Loss:
  name: RFLLoss
  # ignore_index: 0

PostProcess:
  name: RFLLabelDecode

Metric:
  name: RecMetric
  main_indicator: acc

Train:
  dataset:
    name: LMDBDataSet
    data_dir: ./train_data/data_lmdb_release/training
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - RFLLabelEncode: # Class handling label
      - RFLRecResizeImg:
          image_shape: [1, 32, 100]
          padding: false
          interpolation: 2
      - KeepKeys:
          keep_keys: ['image', 'label', 'length', 'cnt_label'] # dataloader will return list in this order
  loader:
    shuffle: True
    batch_size_per_card: 64
    drop_last: True
    num_workers: 8

Eval:
  dataset:
    name: LMDBDataSet
    data_dir: ./train_data/data_lmdb_release/validation/
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - RFLLabelEncode: # Class handling label
      - RFLRecResizeImg:
          image_shape: [1, 32, 100]
          padding: false
          interpolation: 2
      - KeepKeys:
          keep_keys: ['image', 'label', 'length', 'cnt_label'] # dataloader will return list in this order
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 256
    num_workers: 8
