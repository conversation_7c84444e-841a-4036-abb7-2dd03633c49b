# Training configuration
GPU: [0,1,2,3]

VERBOSE: False

MODEL:
  MODE: 'Deraindrop'

# Optimization arguments.
OPTIM:
  BATCH: 2
  EPOCHS: 150
  # NEPOCH_DECAY: [10]
  LR_INITIAL: 2e-4
  LR_MIN: 1e-6
  # BETA1: 0.9

TRAINING:
  VAL_AFTER_EVERY: 1
  RESUME: False
  TRAIN_PS: 256
  VAL_PS: 256
  TRAIN_DIR: './datasets/deraindrop/train'       # path to training data
  VAL_DIR: './datasets/deraindrop/test' # path to validation data
  SAVE_DIR: './checkpoints'           # path to save models and images
