import torch
import psutil
import os

def check_gpu_memory():
    """检查GPU内存使用情况"""
    if torch.cuda.is_available():
        device = torch.cuda.current_device()
        gpu_memory = torch.cuda.get_device_properties(device).total_memory
        gpu_memory_allocated = torch.cuda.memory_allocated(device)
        gpu_memory_reserved = torch.cuda.memory_reserved(device)
        
        print(f"GPU设备: {torch.cuda.get_device_name(device)}")
        print(f"总内存: {gpu_memory / 1024**3:.2f} GB")
        print(f"已分配内存: {gpu_memory_allocated / 1024**3:.2f} GB")
        print(f"已保留内存: {gpu_memory_reserved / 1024**3:.2f} GB")
        print(f"可用内存: {(gpu_memory - gpu_memory_reserved) / 1024**3:.2f} GB")
        print("-" * 50)
    else:
        print("CUDA不可用")

def check_system_memory():
    """检查系统内存使用情况"""
    memory = psutil.virtual_memory()
    print(f"系统总内存: {memory.total / 1024**3:.2f} GB")
    print(f"已使用内存: {memory.used / 1024**3:.2f} GB")
    print(f"可用内存: {memory.available / 1024**3:.2f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    print("-" * 50)

def clear_gpu_cache():
    """清理GPU缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        print("GPU缓存已清理")
    else:
        print("CUDA不可用，无法清理GPU缓存")

if __name__ == "__main__":
    print("=== 内存检查 ===")
    check_system_memory()
    check_gpu_memory()
    
    print("\n=== 清理GPU缓存 ===")
    clear_gpu_cache()
    
    print("\n=== 清理后的GPU内存状态 ===")
    check_gpu_memory()
