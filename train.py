import warnings
import os
import torch
warnings.filterwarnings("ignore")
from ultralytics import YOLO

# 设置环境变量来解决内存碎片化问题
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

def clear_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

if __name__ == "__main__":
    # 清理GPU内存
    clear_gpu_memory()

    model = YOLO(model=r'F:\qyp\ultralytics\ultralytics\cfg\models\11\try1.yaml')  # load a pretrained model (recommended for training)

    # 减少批次大小和图像尺寸来节省内存
    model.train(data=r"F:\qyp\ultralytics\ultralytics\cfg\datasets\ronghe.yaml",
                imgsz=416,  # 从640减少到416
                epochs=10,
                batch=4,    # 从8减少到4
                workers=2,  # 从4减少到2
                device=0,
                amp=True,   # 启用混合精度训练
                )  # train the model

    # 训练完成后清理内存
    clear_gpu_memory()

    model.val(data=r"F:\qyp\ultralytics\ultralytics\cfg\datasets\ronghe.yaml",
          imgsz=416,  # 保持与训练时相同的尺寸
          batch=4,    # 减少验证批次大小
          device=0)