name: PaddleOCR Code Style Check

on:
  pull_request:
  push:
    branches: ['main', 'release/*']

jobs:
  check-code-style:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.ref }}
    - uses: actions/setup-python@v5
      with:
        python-version: '3.10'
    # Install Dependencies for Python
    - name: Install Dependencies for Python
      run: |
        python -m pip install --upgrade pip
        pip install "clang-format==13.0.0"
    - uses: pre-commit/action@v3.0.1
